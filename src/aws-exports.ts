import { ResourcesConfig } from '@aws-amplify/core';

const awsExports: ResourcesConfig = {
  Auth: {
    Cognito: {
      userPoolId: 'us-east-2_DHNnMYsNY', // Cognito User Pool ID
      userPoolClientId: '5pm5gsq31ki0bmpnflara9f8rq', // Cognito App Client ID
      loginWith: {
        oauth: {
          domain: 'stage-auth-domain.auth.us-east-2.amazoncognito.com',
          scopes: ['openid', 'email', 'profile'],
          redirectSignIn: ['jfg://callback', 'http://localhost:8081/'],
          redirectSignOut: ['jfg://callback', 'http://localhost:8081/'],
          responseType: 'code',
        },
      },
    },
  },
};

export default awsExports;
