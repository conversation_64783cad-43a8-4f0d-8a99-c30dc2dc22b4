import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect } from 'react';
import { callApi } from '../api/client';
import { useManagerQuery, useTeam } from '../hooks/useQueries';
import { Manager } from '../models/manager';
import { Team } from '../models/team';

// Storage keys
export const MANAGER_ID_KEY = '@manager_id';
export const TEAM_ID_KEY = '@team_id';
export const GAMEWORLD_ID_KEY = '@gameworld_id';
export const NOTIFICATION_SETTINGS_SEEN_KEY = '@notification_settings_seen';
export const CACHED_FIRST_NAME_KEY = '@cached_first_name';
export const CACHED_LAST_NAME_KEY = '@cached_last_name';
export const PROFILE_NEEDS_UPDATE_KEY = '@profile_needs_update';
export const LAST_READ_MESSAGE_ID_KEY = '@last_read_message_id';
export const LAST_READ_MESSAGE_DATE_KEY = '@last_read_message_date';

interface ManagerContextType {
  manager: Manager | null;
  team: Team | null;
  loading: boolean;
  error: Error | null;
  storedIds: {
    managerId?: string;
    teamId?: string;
    gameworldId?: string;
  };
  refreshManager: () => Promise<void>;
}

const ManagerContext = createContext<ManagerContextType | undefined>(undefined);

async function saveManagerProfile(manager: Manager) {
  const [cachedFirstName, cachedLastName] = await Promise.all([
    AsyncStorage.getItem(CACHED_FIRST_NAME_KEY),
    AsyncStorage.getItem(CACHED_LAST_NAME_KEY),
  ]);

  if (cachedFirstName && cachedLastName) {
    try {
      // Auto-save the cached names to the API
      await callApi('/manager/name', {
        method: 'PUT',
        body: JSON.stringify({
          firstName: cachedFirstName.trim(),
          lastName: cachedLastName.trim(),
        }),
      });

      manager.firstName = cachedFirstName;
      manager.lastName = cachedLastName;

      // Clear the flag since we've updated the profile
      await AsyncStorage.setItem(PROFILE_NEEDS_UPDATE_KEY, 'false');
    } catch (error) {
      console.error('Failed to auto-save profile:', error);
    }

    return manager;
  }
}

export const ManagerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State to store locally retrieved IDs (useful for offline functionality)
  const [storedIds, setStoredIds] = React.useState<{
    managerId?: string;
    teamId?: string;
    gameworldId?: string;
  }>({});

  // Load stored IDs from AsyncStorage on initial mount
  useEffect(() => {
    const loadStoredIds = async () => {
      try {
        const [managerId, teamId, gameworldId] = await Promise.all([
          AsyncStorage.getItem(MANAGER_ID_KEY),
          AsyncStorage.getItem(TEAM_ID_KEY),
          AsyncStorage.getItem(GAMEWORLD_ID_KEY),
        ]);

        if (managerId && teamId && gameworldId) {
          setStoredIds({ managerId, teamId, gameworldId });
          console.log('Loaded stored IDs from AsyncStorage');
        }
      } catch (error) {
        console.error('Error loading stored IDs from AsyncStorage:', error);
      }
    };

    loadStoredIds();
  }, []);

  // Use React Query to fetch manager data
  const {
    data: manager,
    isLoading: managerLoading,
    error: managerError,
    refetch,
  } = useManagerQuery();

  // Use React Query to fetch team data once we have the manager
  const {
    data: team,
    isLoading: teamLoading,
    error: teamError,
  } = useTeam(manager?.gameworldId, manager?.team?.teamId);

  // Save manager and team IDs to AsyncStorage when they are retrieved
  useEffect(() => {
    const saveManagerData = async () => {
      if (manager?.managerId && manager?.team && manager?.gameworldId) {
        try {
          await AsyncStorage.setItem(MANAGER_ID_KEY, manager.managerId);
          await AsyncStorage.setItem(TEAM_ID_KEY, manager.team.teamId);
          await AsyncStorage.setItem(GAMEWORLD_ID_KEY, manager.gameworldId);
          console.log('Saved manager data to AsyncStorage');
        } catch (error) {
          console.error('Error saving manager data to AsyncStorage:', error);
        }

        // save the players name if necessary
        const needsUpdate = await AsyncStorage.getItem(PROFILE_NEEDS_UPDATE_KEY);
        if (needsUpdate === 'true') {
          await saveManagerProfile(manager);
        }
      }
    };

    saveManagerData();
  }, [manager]);

  const refreshManager = async () => {
    await refetch(); // Trigger a refetch of the manager data
  };

  return (
    <ManagerContext.Provider
      value={{
        manager: manager || null,
        team: team || null,
        loading: managerLoading || teamLoading,
        error: managerError || teamError || null,
        storedIds,
        refreshManager, // Provide refreshManager in the context
      }}
    >
      {children}
    </ManagerContext.Provider>
  );
};

export const useManager = () => {
  const context = useContext(ManagerContext);
  if (context === undefined) {
    throw new Error('useManager must be used within a ManagerProvider');
  }
  return context;
};
