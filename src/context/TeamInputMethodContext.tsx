import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';

const TEAM_INPUT_METHOD_KEY = '@team_input_method';

export type TeamInputMethod = 'drag' | 'tap';

interface TeamInputMethodContextType {
  inputMethod: TeamInputMethod;
  setInputMethod: (method: TeamInputMethod) => Promise<void>;
}

const TeamInputMethodContext = createContext<TeamInputMethodContextType>({
  inputMethod: 'drag',
  setInputMethod: async () => {},
});

export const useTeamInputMethod = () => useContext(TeamInputMethodContext);

export const TeamInputMethodProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [inputMethod, setInputMethodState] = useState<TeamInputMethod>('drag');

  useEffect(() => {
    const loadInputMethod = async () => {
      try {
        const savedMethod = await AsyncStorage.getItem(TEAM_INPUT_METHOD_KEY);
        if (savedMethod && (savedMethod === 'drag' || savedMethod === 'tap')) {
          setInputMethodState(savedMethod as TeamInputMethod);
        }
      } catch (error) {
        console.warn('Failed to load team input method preference:', error);
      }
    };

    loadInputMethod();
  }, []);

  const setInputMethod = async (method: TeamInputMethod) => {
    try {
      await AsyncStorage.setItem(TEAM_INPUT_METHOD_KEY, method);
      setInputMethodState(method);
    } catch (error) {
      console.warn('Failed to save team input method preference:', error);
    }
  };

  return (
    <TeamInputMethodContext.Provider
      value={{
        inputMethod,
        setInputMethod,
      }}
    >
      {children}
    </TeamInputMethodContext.Provider>
  );
};
