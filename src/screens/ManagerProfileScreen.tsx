import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
//import { englishDataset, englishRecommendedTransformers, RegExpMatcher } from 'obscenity';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { Description } from '../components/Common';
import { CrossPlatformAlert } from '../components/CrossPlatformAlert';
import { Text } from '../components/Text';
import {
  CACHED_FIRST_NAME_KEY,
  CACHED_LAST_NAME_KEY,
  PROFILE_NEEDS_UPDATE_KEY,
  useManager,
} from '../context/ManagerContext';
import { useTheme } from '../theme/ThemeContext';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 16px;
`;

const FormContainer = styled.View`
  margin-top: 24px;
`;

const InputContainer = styled.View`
  margin-bottom: 16px;
`;

const Label = styled(Text)`
  font-size: 16px;
  margin-bottom: 8px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const Input = styled.TextInput`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 12px 16px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props: StyledProps) => props.theme.colors.border};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-size: 16px;
`;

const SaveButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 16px;
  border-radius: 8px;
  align-items: center;
  margin-top: 24px;
`;

const ButtonText = styled(Text)`
  color: white;
  font-size: 16px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const ManagerProfileScreen = () => {
  const { manager, refreshManager } = useManager();
  const { theme } = useTheme();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [dialogMessage, setDialogMessage] = useState<{ title: string; message: string }>({
    title: '',
    message: '',
  });

  /*  const matcher = new RegExpMatcher({
    ...englishDataset.build(),
    ...englishRecommendedTransformers,
  });*/

  // Load cached names or manager data on initial render
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        // Try to load cached names first
        const [cachedFirstName, cachedLastName] = await Promise.all([
          AsyncStorage.getItem(CACHED_FIRST_NAME_KEY),
          AsyncStorage.getItem(CACHED_LAST_NAME_KEY),
        ]);

        // If we have manager data, use that (it's the source of truth)
        if (manager?.firstName || manager?.lastName) {
          setFirstName(manager.firstName || '');
          setLastName(manager.lastName || '');
        }
        // Otherwise use cached data if available
        else if (cachedFirstName || cachedLastName) {
          setFirstName(cachedFirstName || '');
          setLastName(cachedLastName || '');
        }

        setIsInitialized(true);
      } catch (error) {
        console.error('Error loading profile data:', error);
        // Fall back to manager data if available
        if (manager) {
          setFirstName(manager.firstName || '');
          setLastName(manager.lastName || '');
        }
        setIsInitialized(true);
      }
    };

    loadProfileData();
  }, [manager]);

  const handleSave = async () => {
    if (!firstName.trim() || !lastName.trim()) {
      if (Platform.OS === 'web') {
        setShowConfirmation(true);
        setDialogMessage({ title: 'Error', message: 'Please enter both first and last name' });
      } else {
        Alert.alert('Error', 'Please enter both first and last name');
      }
      return;
    }

    /*if (matcher.hasMatch(firstName) || matcher.hasMatch(lastName)) {
      if (Platform.OS === 'web') {
        setShowConfirmation(true);
        setDialogMessage({ title: 'Error', message: 'Very rude' });
      } else {
        Alert.alert('Error', 'Very rude');
      }
      return;
    }*/

    setIsSaving(true);
    try {
      // Always cache the names regardless of manager availability
      await Promise.all([
        AsyncStorage.setItem(CACHED_FIRST_NAME_KEY, firstName.trim()),
        AsyncStorage.setItem(CACHED_LAST_NAME_KEY, lastName.trim()),
      ]);

      // If manager exists, update via API
      if (manager?.managerId) {
        const response = await callApi('/manager/name', {
          method: 'PUT',
          body: JSON.stringify({
            firstName: firstName.trim(),
            lastName: lastName.trim(),
          }),
        });

        console.log('Profile update response:', response);

        // Invalidate the manager query to refetch the updated data
        await refreshManager();

        // Clear the update flag since we've successfully updated the profile
        await AsyncStorage.setItem(PROFILE_NEEDS_UPDATE_KEY, 'false');
      } else {
        // If manager doesn't exist yet, set flag to update later
        await AsyncStorage.setItem(PROFILE_NEEDS_UPDATE_KEY, 'true');
      }

      if (Platform.OS === 'web') {
        setShowConfirmation(true);
        setDialogMessage({ title: 'Success', message: 'Profile saved successfully' });
      } else {
        Alert.alert('Success', 'Profile saved successfully');
      }
      router.replace('/');
    } catch (error) {
      console.error('Error saving profile:', error);
      if (Platform.OS === 'web') {
        setShowConfirmation(true);
        if ((error as any).status === 406) {
          setDialogMessage({ title: 'Error', message: 'Rude!' });
        } else {
          setDialogMessage({
            title: 'Error',
            message: 'Failed to save profile. Please try again.',
          });
        }
      } else {
        Alert.alert('Error', 'Failed to save profile. Please try again.');
      }
    } finally {
      setIsSaving(false);
    }
  };

  if (!isInitialized) {
    return (
      <Container>
        <ActivityIndicator size="large" color={theme.colors.primary} testID="loading-indicator" />
      </Container>
    );
  }

  return (
    <Container>
      <Description>
        Welcome to the job center. We'll have you back doing... *checks notes*... owning and
        managing an amateur football club in no time. First things first.... who are you?!
      </Description>
      <FormContainer>
        <InputContainer>
          <Label>First Name</Label>
          <Input
            value={firstName}
            onChangeText={setFirstName}
            placeholder="Enter your first name"
            placeholderTextColor={theme.colors.text.secondary}
          />
        </InputContainer>

        <InputContainer>
          <Label>Last Name</Label>
          <Input
            value={lastName}
            onChangeText={setLastName}
            placeholder="Enter your last name"
            placeholderTextColor={theme.colors.text.secondary}
          />
        </InputContainer>

        <SaveButton onPress={handleSave} disabled={isSaving}>
          {isSaving ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <ButtonText>Save Profile</ButtonText>
          )}
        </SaveButton>
      </FormContainer>

      <CrossPlatformAlert
        visible={showConfirmation}
        title={dialogMessage.title}
        message={dialogMessage.message}
        buttons={[
          {
            text: 'OK',
            onPress: () => setShowConfirmation(false),
          },
        ]}
        onDismiss={() => setShowConfirmation(false)}
      />
    </Container>
  );
};

export default ManagerProfileScreen;
