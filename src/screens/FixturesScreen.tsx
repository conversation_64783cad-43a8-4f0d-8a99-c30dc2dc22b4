import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useRouter } from 'expo-router';
import React from 'react';
import { ActivityIndicator, TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { useFixtures } from '../hooks/useQueries';
import { Scorer } from '../models/fixture';

dayjs.extend(relativeTime);

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.ScrollView`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 16px;
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const NextFixtureText = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin-bottom: 16px;
`;

const FixtureCard = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const FixtureRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
`;

const TeamContainer = styled.View<{ isHome?: boolean }>`
  flex: 1;
  align-items: ${({ isHome }: { isHome?: boolean }) => (isHome ? 'flex-end' : 'flex-start')};
`;

const TeamName = styled(Text)<{ isCurrentTeam?: boolean }>`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${({ isCurrentTeam }: { isCurrentTeam?: boolean }) =>
    isCurrentTeam ? 'NunitoBold' : 'Nunito'};
`;

const Score = styled.View`
  flex-direction: row;
  justify-content: center;
  width: 80px;
`;

const ScoreText = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const DateText = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: center;
  margin-bottom: 8px;
`;

const ScorerText = styled(Text)<{ isHome?: boolean }>`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: ${({ isHome }: { isHome?: boolean }) => (isHome ? 'right' : 'left')};
`;

const ErrorText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.error};
  font-family: 'Nunito';
  text-align: center;
`;

const formatMatchTime = (minute: number, half: number) => {
  const adjustedMinute = half === 2 ? minute + 45 : minute;
  return `${adjustedMinute}'`;
};

const TeamScorers = ({ scorers, team }: { scorers: Scorer[]; team: number }) => {
  const teamScorers = scorers
    .filter((scorer) => scorer.team === team)
    .map((scorer) => {
      const times = scorer.goalTime
        .map((time) => formatMatchTime(time.minute, time.half))
        .join(', ');
      return `${scorer.playerName} ${times}`;
    });

  return <ScorerText isHome={team === 0}>{teamScorers.join('\n')}</ScorerText>;
};

const FixturesScreen = () => {
  const { manager, team } = useManager();
  const router = useRouter();

  const { data, isLoading, error } = useFixtures(
    manager?.gameworldId,
    team?.league.id,
    team?.teamId
  );

  if (isLoading) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  if (error) {
    return (
      <Container>
        <ErrorText>Failed to load fixtures</ErrorText>
      </Container>
    );
  }

  const getNextFixture = () => {
    const now = dayjs();
    if (!data || data.length === 0) return null;
    return data.find((fixture) => dayjs(fixture.date) > now);
  };

  const getTimeToNextFixture = () => {
    const nextFixture = getNextFixture();
    if (!nextFixture) return null;

    const now = dayjs();
    const fixtureTime = dayjs(nextFixture.date);
    const hours = fixtureTime.diff(now, 'hour');
    const minutes = fixtureTime.diff(now, 'minute') % 60;

    if (hours > 0) {
      return `Next fixture in ${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`;
    }
    return `Next fixture in ${minutes}m`;
  };

  const nextFixtureTime = getTimeToNextFixture();

  return (
    <Container>
      {nextFixtureTime && <NextFixtureText>{nextFixtureTime}</NextFixtureText>}
      {data?.map((fixture) => (
        <TouchableOpacity
          key={fixture.fixtureId}
          onPress={() => fixture.score && router.push(`/fixture-detail/${fixture.fixtureId}`)}
          disabled={!fixture.score}
        >
          <FixtureCard>
            <DateText>{dayjs(fixture.date).format('ddd D MMM HH:mm')}</DateText>
            <FixtureRow>
              <TeamContainer isHome>
                <TeamName isCurrentTeam={fixture.homeTeamName === team?.teamName}>
                  {fixture.homeTeamName}
                </TeamName>
              </TeamContainer>
              <Score>
                <ScoreText>
                  {fixture.score ? `${fixture.score[0]} - ${fixture.score[1]}` : 'v'}
                </ScoreText>
              </Score>
              <TeamContainer>
                <TeamName isCurrentTeam={fixture.awayTeamName === team?.teamName}>
                  {fixture.awayTeamName}
                </TeamName>
              </TeamContainer>
            </FixtureRow>
            {fixture.scorers && (
              <FixtureRow>
                <TeamContainer isHome>
                  <TeamScorers scorers={fixture.scorers} team={0} />
                </TeamContainer>
                <Score />
                <TeamContainer>
                  <TeamScorers scorers={fixture.scorers} team={1} />
                </TeamContainer>
              </FixtureRow>
            )}
          </FixtureCard>
        </TouchableOpacity>
      ))}
    </Container>
  );
};

export default FixturesScreen;
