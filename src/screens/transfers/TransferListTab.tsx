import { FontAwesome } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, Platform, View } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import PlayerDetailView from '../../components/PlayerDetailView';
import PlayerCard from '../../components/PlayerRow/PlayerCard';
import PositionFilter, {
  PositionFilter as PositionFilterType,
} from '../../components/PositionFilter/PositionFilter';
import { Text } from '../../components/Text';
import { useManager } from '../../context/ManagerContext';
import { useMyBidsPlayers } from '../../hooks/useMyBidsPlayers';
import { useTeam } from '../../hooks/useQueries';
import { TransferListPlayer, useTransferListPlayers } from '../../hooks/useTransferListPlayers';
import { PlayerLike } from '../../utils/PlayerUtils';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 16px;
  /* Establish a stacking context for proper z-index handling */
  ${Platform.OS === 'web' ? 'position: relative;' : ''}
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

const ListContainer = styled.View`
  flex: 1;
  margin-top: 16px;
  z-index: 1; /* Lower z-index to ensure dropdown appears above */
`;

const ListHeaderText = styled(Text)`
  font-size: 18px;
  font-family: 'NunitoBold';
  margin-bottom: 8px;
`;

const EmptyListContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const EmptyListText = styled(Text)`
  font-size: 16px;
  text-align: center;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
`;

interface LoadMoreButtonProps extends StyledProps {
  disabled?: boolean;
}

const LoadMoreButton = styled.TouchableOpacity<LoadMoreButtonProps>`
  background-color: ${(props) =>
    props.disabled ? props.theme.colors.primary + '80' : props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  margin: 16px 0;
  align-self: center;
  min-width: 150px;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 16px;
`;

const TransferListTab = () => {
  const { manager } = useManager();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [lastEvaluatedKey, setLastEvaluatedKey] = useState<string | undefined>(undefined);
  const [transferListPlayers, setTransferListPlayers] = useState<TransferListPlayer[]>([]);
  const [myBidsPlayers, setMyBidsPlayers] = useState<TransferListPlayer[]>([]);
  const [selectedPlayer, setSelectedPlayer] = useState<TransferListPlayer | null>(null);
  const [positionFilter, setPositionFilter] = useState<PositionFilterType>('All');

  // Log manager info for debugging
  useEffect(() => {
    console.log('Manager info:', manager);
    console.log('GameworldId:', manager?.gameworldId);
  }, [manager]);
  const [teamAverages, setTeamAverages] = useState<
    Record<PositionFilterType, Record<string, number>>
  >({
    All: {},
    Goalkeeper: {},
    Defender: {},
    Midfielder: {},
    Attacker: {},
  });

  const { data: transferListData, isLoading: isLoadingTransferList } = useTransferListPlayers(
    manager?.gameworldId,
    lastEvaluatedKey
  );

  const { data: myBidsData, isLoading: isLoadingMyBids } = useMyBidsPlayers(manager?.gameworldId);

  const { data: teamData, isLoading: isLoadingTeam } = useTeam(
    manager?.gameworldId,
    manager?.team?.teamId
  );

  // Track if this is the initial load or a pagination load
  const isInitialLoad = React.useRef(true);

  // Effect for handling my bids data
  useEffect(() => {
    if (myBidsData) {
      console.log('My bids data received:', myBidsData);
      const players = myBidsData.players || [];
      setMyBidsPlayers(players);
    }
  }, [myBidsData]);

  useEffect(() => {
    if (transferListData) {
      console.log('Transfer list data received:', transferListData);
      console.log('Current lastEvaluatedKey:', lastEvaluatedKey);
      console.log('Is initial load:', isInitialLoad.current);
      console.log('Is loading more:', isLoadingMore);

      // Check if players property exists, if not, try to handle different response structures
      const players = transferListData.players || [];

      // Check if we've reached the end of the list
      const paginationKey = getPaginationKey(transferListData);
      if (isLoadingMore && players.length === 0) {
        console.log('Received empty page, reached end of list');
        setReachedEnd(true);
        return;
      }

      if (isInitialLoad.current || (!lastEvaluatedKey && !isLoadingMore)) {
        // This is the initial load
        console.log('Setting initial players:', players.length);
        setTransferListPlayers(players);
        isInitialLoad.current = false;

        // Reset reached end flag on initial load
        setReachedEnd(false);
      } else if (isLoadingMore || lastEvaluatedKey) {
        // This is a pagination load
        console.log('Adding more players:', players.length);
        setTransferListPlayers((prev) => {
          if (!Array.isArray(prev)) {
            console.warn('Previous players is not an array:', prev);
            return players;
          }
          if (!Array.isArray(players)) {
            console.warn('New players is not an array:', players);
            return prev;
          }

          // Filter out duplicates before combining
          const existingPlayerIds = new Set(prev.map((player) => player.playerId));
          const uniqueNewPlayers = players.filter(
            (player) => !existingPlayerIds.has(player.playerId)
          );

          console.log('Existing players:', prev.length);
          console.log('New unique players:', uniqueNewPlayers.length);

          // If we got no new unique players, we've reached the end
          if (uniqueNewPlayers.length === 0 && isLoadingMore) {
            console.log('No new unique players, reached end of list');
            setReachedEnd(true);
          }

          // Combine previous and unique new players
          const combinedPlayers = [...prev, ...uniqueNewPlayers];
          console.log('Combined players count:', combinedPlayers.length);
          return combinedPlayers;
        });
      }
    }
  }, [transferListData, isLoadingMore, lastEvaluatedKey]);

  // Log pagination info for debugging
  useEffect(() => {
    if (transferListData) {
      const paginationKey = getPaginationKey(transferListData);
      console.log('Pagination key found:', paginationKey);
      if (paginationKey) {
        console.log('More players available, can load more');
      } else {
        console.log('No more players available');
      }
    }
  }, [transferListData]);

  useEffect(() => {
    if (teamData?.players) {
      const calculatePositionAverages = (players: PlayerLike[], attributes: string[]) => {
        const result: Record<string, number> = {};

        attributes.forEach((attr) => {
          const values = players.map(
            (player) => player.attributes[attr as keyof typeof player.attributes] || 0
          );
          const sum = values.reduce((acc, val) => acc + val, 0);
          result[attr] = values.length > 0 ? sum / values.length : 0;
        });

        return result;
      };

      const averages: Record<PositionFilterType, Record<string, number>> = {
        All: calculatePositionAverages(teamData.players, [
          'reflexes',
          'positioning',
          'shotStopping',
          'tackling',
          'marking',
          'heading',
          'passing',
          'vision',
          'ballControl',
          'finishing',
          'pace',
          'crossing',
        ]),
        Goalkeeper: calculatePositionAverages(teamData.players, [
          'reflexes',
          'positioning',
          'shotStopping',
        ]),
        Defender: calculatePositionAverages(teamData.players, ['tackling', 'marking', 'heading']),
        Midfielder: calculatePositionAverages(teamData.players, [
          'passing',
          'vision',
          'ballControl',
        ]),
        Attacker: calculatePositionAverages(teamData.players, ['finishing', 'pace', 'crossing']),
      };

      setTeamAverages(averages);
    }
  }, [teamData?.players]);

  // Function to handle position filter selection
  const handlePositionSelect = (position: PositionFilterType) => {
    setPositionFilter(position);
  };

  // Track if we've reached the end of the list
  const [reachedEnd, setReachedEnd] = useState(false);

  // Helper function to get the pagination key from various possible locations
  const getPaginationKey = (data: any): string | undefined => {
    if (!data) return undefined;
    if (reachedEnd) return undefined; // Don't return a key if we've reached the end

    // Check in pagination object
    if (data.pagination) {
      if (data.pagination.lastEvaluatedKey) return data.pagination.lastEvaluatedKey;
      if (data.pagination.nextToken) return data.pagination.nextToken;
      if (data.pagination.pageId) return data.pagination.pageId;
    }

    // Check at root level
    if (data.lastEvaluatedKey) return data.lastEvaluatedKey;
    if (data.nextToken) return data.nextToken;
    if (data.pageId) return data.pageId;

    return undefined;
  };

  const loadMorePlayers = () => {
    // Don't try to load more if we've reached the end
    if (reachedEnd) {
      console.log('Already reached end of list, not loading more');
      return;
    }

    const paginationKey = getPaginationKey(transferListData);

    if (paginationKey && paginationKey !== lastEvaluatedKey) {
      console.log('Loading more players with key:', paginationKey);
      console.log('Previous key was:', lastEvaluatedKey);
      setIsLoadingMore(true); // Set loading more state to true
      setLastEvaluatedKey(paginationKey);
    } else if (paginationKey === lastEvaluatedKey) {
      console.log('Already using this pagination key, not loading more');
    } else {
      console.log('No more players to load');
      setReachedEnd(true); // Mark that we've reached the end
    }
  };

  // Function to check if the user's team is the highest bidder
  const isUserHighestBidder = (player: TransferListPlayer): boolean => {
    if (!manager?.team || !player.bidHistory || player.bidHistory.length === 0) {
      return false;
    }

    // Sort bid history by maximum bid (highest first)
    const sortedBids = [...player.bidHistory].sort((a, b) => b.maximumBid - a.maximumBid);

    // Check if the user's team is the highest bidder
    return sortedBids[0]?.teamId === manager.team.teamId;
  };

  const renderPlayer = ({ item }: { item: TransferListPlayer; index: number }) => {
    const handlePlayerSelect = (player: PlayerLike) => {
      setSelectedPlayer(player as TransferListPlayer);
    };

    return (
      <PlayerCard
        player={item}
        onSelect={handlePlayerSelect}
        isSelected={selectedPlayer?.playerId === item.playerId}
        showValue={true}
        positionFilter={positionFilter}
        teamAverages={teamAverages[positionFilter]}
        isTransferList={true}
        isHighestBidder={isUserHighestBidder(item)}
      />
    );
  };

  const renderEmptyList = () => (
    <EmptyListContainer>
      <EmptyListText>No players currently available on the transfer list.</EmptyListText>
    </EmptyListContainer>
  );

  // Update loading states based on query loading states
  useEffect(() => {
    console.log('Loading states:', {
      isLoadingTransferList,
      isLoadingMyBids,
      isLoadingTeam,
      isLoadingMore,
    });

    // Only set isLoading to true for initial load, not for pagination
    if (!isLoadingMore) {
      setIsLoading(isLoadingTransferList || isLoadingMyBids || isLoadingTeam);
    }

    // If we were loading more and the loading has finished, reset the loading more state
    if (isLoadingMore && !isLoadingTransferList) {
      console.log('Finished loading more, resetting isLoadingMore');
      setIsLoadingMore(false);
    }
  }, [isLoadingTransferList, isLoadingMyBids, isLoadingTeam, isLoadingMore]);

  if (isLoading) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  const renderEmptyMyBidsList = () => (
    <EmptyListContainer>
      <EmptyListText>You haven't placed any bids yet.</EmptyListText>
    </EmptyListContainer>
  );

  // Create a combined data structure with sections
  const combinedData = [];

  // Add my bids section if there are any bids
  if (myBidsPlayers.length > 0) {
    combinedData.push({
      title: "Players You've Bid On",
      data: myBidsPlayers,
      type: 'myBids',
    });
  }

  // Add transfer list section
  combinedData.push({
    title: 'Players Available for Transfer',
    data: transferListPlayers,
    type: 'transferList',
  });

  // Render a section header
  const renderSectionHeader = ({ section }: { section: { title: string } }) => (
    <ListHeaderText style={{ marginTop: 16, marginBottom: 8 }}>{section.title}</ListHeaderText>
  );

  // Render a section footer (for the Load More button in the transfer list section)
  const renderSectionFooter = ({ section }: { section: { type: string } }) => {
    if (section.type === 'transferList' && getPaginationKey(transferListData)) {
      return (
        <LoadMoreButton onPress={loadMorePlayers} disabled={isLoadingMore}>
          {isLoadingMore ? (
            <>
              <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
              <ButtonText>Loading...</ButtonText>
            </>
          ) : (
            <ButtonText>Load More</ButtonText>
          )}
        </LoadMoreButton>
      );
    }
    return null;
  };

  // Render an empty component based on section type
  const renderEmptyComponent = ({ section }: { section: { type: string } }) => {
    if (section.type === 'myBids') {
      return renderEmptyMyBidsList();
    } else {
      return renderEmptyList();
    }
  };

  return (
    <Container>
      <PositionFilter positionFilter={positionFilter} onPositionSelect={handlePositionSelect} />

      {/* Combined List with Sections */}
      <ListContainer style={Platform.OS === 'web' ? { position: 'relative', zIndex: 1 } : {}}>
        <FlatList
          data={combinedData.flatMap((section) =>
            section.data.length > 0
              ? [{ isHeader: true, title: section.title, type: section.type }, ...section.data]
              : [
                  { isHeader: true, title: section.title, type: section.type },
                  { isEmpty: true, type: section.type },
                ]
          )}
          renderItem={({ item }) => {
            if (item.isHeader) {
              return (
                <ListHeaderText style={{ marginTop: 16, marginBottom: 8 }}>
                  {item.title}
                </ListHeaderText>
              );
            }

            if (item.isEmpty) {
              return item.type === 'myBids' ? renderEmptyMyBidsList() : renderEmptyList();
            }

            return renderPlayer({ item: item as TransferListPlayer, index: 0 });
          }}
          keyExtractor={(item, index) => {
            if (item.isHeader || item.isEmpty) return `${item.type}-${index}`;
            return (item as TransferListPlayer).playerId;
          }}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
          ListFooterComponent={() =>
            getPaginationKey(transferListData) ? (
              <LoadMoreButton onPress={loadMorePlayers} disabled={isLoadingMore}>
                {isLoadingMore ? (
                  <>
                    <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
                    <ButtonText>Loading...</ButtonText>
                  </>
                ) : (
                  <ButtonText>Load More</ButtonText>
                )}
              </LoadMoreButton>
            ) : null
          }
        />
      </ListContainer>

      {/* Player detail view rendered outside of ListContainer for proper z-index handling */}
      {selectedPlayer && (
        <PlayerDetailView player={selectedPlayer} onClose={() => setSelectedPlayer(null)} />
      )}
    </Container>
  );
};

export default TransferListTab;
