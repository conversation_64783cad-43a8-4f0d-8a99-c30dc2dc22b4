import { MaterialIcons } from '@expo/vector-icons';
import { Skeleton } from 'moti/skeleton';
import React, { useEffect, useState } from 'react';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { CrossPlatformAlert } from '../components/CrossPlatformAlert';
import { Text } from '../components/Text';
import TrainingModal from '../components/TrainingModal';
import TrainingSlot, { ArrowContainer } from '../components/TrainingSlot';
import { useManager } from '../context/ManagerContext';
import { useTeam } from '../hooks/useQueries';
import {
  TrainingSlotData,
  useAssignTrainingSlot,
  useTrainingSlots,
  useUnlockTrainingSlot,
} from '../hooks/useTrainingSlots';
import { Player } from '../models/player';
import { formatPlayerValue } from '../utils/PlayerUtils';

const Spacer = ({ height = 16 }) => <View style={{ height }} />;

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;
const Header = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  padding: 20px;
  margin: 16px;
  border-radius: 12px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 2px;
`;

const HeaderTitle = styled(Text)<StyledProps>`
  font-size: 20px;
  font-family: 'NunitoBold';
  color: ${(props) => props.theme.colors.text.primary};
  text-align: center;
  margin-bottom: 16px;
`;

const QualityContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const QualityInfo = styled.View`
  flex: 1;
`;

const QualityLabel = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
`;

const QualityValue = styled(Text)<StyledProps>`
  font-size: 18px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const UpgradeButton = styled(TouchableOpacity)<StyledProps>`
  background-color: ${(props) => props.theme.colors.primary};
  padding: 12px 20px;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
`;

const UpgradeButtonText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.surface};
  font-family: 'NunitoBold';
  margin-left: 8px;
`;

const SlotsContainer = styled.View`
  flex: 1;
`;

const SectionTitle = styled(Text)<StyledProps>`
  font-size: 18px;
  font-family: 'NunitoBold';
  color: ${(props) => props.theme.colors.text.primary};
  margin: 16px 16px 8px 16px;
`;

const SkeletonSlotContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${(props) => props.theme.colors.surface};

  border-radius: 8px;
  padding: 16px;
  margin: 8px 16px;
  min-height: 120px;

  border-color: ${(props) => props.theme.colors.text.secondary};
  opacity: ${() => 0.6};
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 2px;
`;
styled(Skeleton)`
  border-radius: 50px;
`;
const SkeletonSlotContent = styled.View`
  flex: 1;
`;

const TrainingScreen = () => {
  const { team, loading, manager } = useManager();
  const { data: teamData } = useTeam(manager?.gameworldId, manager?.team?.teamId);

  // Training ground state
  const [trainingGroundQuality, setTrainingGroundQuality] = useState(
    manager?.team?.trainingLevel || 1
  );
  const [teamBalance, setTeamBalance] = useState(team?.balance || 0);
  useEffect(() => {
    setTeamBalance(team?.balance || 0);
  }, [team?.balance]);

  const [showUpgradeAlert, setShowUpgradeAlert] = useState(false);
  const [showTrainingModal, setShowTrainingModal] = useState(false);
  const [selectedSlotIndex, setSelectedSlotIndex] = useState<number | null>(null);
  const [showUnlockAlert, setShowUnlockAlert] = useState(false);
  const [slotToUnlock, setSlotToUnlock] = useState<number | null>(null);
  const [errorDialog, setErrorDialog] = useState<string | null>(null);

  // Use new hook for training slots
  const { data: trainingSlots, isLoading: slotsLoading } = useTrainingSlots(teamData?.teamId);
  const unlockSlotMutation = useUnlockTrainingSlot();
  const assignSlotMutation = useAssignTrainingSlot();

  const upgradeQualityCost = trainingGroundQuality * 100000; // £100k per level

  const handleSlotPress = (slotIndex: number) => {
    const slot = trainingSlots?.slots[slotIndex];
    if (!slot) return;
    if (slot.locked) {
      setSlotToUnlock(slotIndex);
      setShowUnlockAlert(true);
    } else {
      setSelectedSlotIndex(slotIndex);
      setShowTrainingModal(true);
    }
  };

  const handleUnlockSlot = async () => {
    if (slotToUnlock !== null) {
      const slot = trainingSlots?.slots[slotToUnlock];
      if (!slot) return;
      const cost = slot.unlockCost || 0;
      if ((teamBalance || 0) >= cost) {
        try {
          const res = await unlockSlotMutation.mutateAsync(slot.slotId);
          if (res?.success) {
            if (typeof res.balance === 'number') setTeamBalance(res.balance);
          } else {
            setErrorDialog('Failed to unlock slot.');
          }
        } catch (error: any) {
          setErrorDialog(error?.message || 'Failed to unlock slot.');
        }
      } else {
        setErrorDialog('Insufficient funds');
      }
    }
    setSlotToUnlock(null);
    setShowUnlockAlert(false);
  };

  const handleAssignPlayer = async (player: Player, attribute: string) => {
    if (selectedSlotIndex !== null) {
      const slot = trainingSlots?.slots[selectedSlotIndex];
      if (!slot) return;
      try {
        await assignSlotMutation.mutateAsync({
          slotId: slot.id,
          playerId: player.playerId,
          attribute,
          slotIndex: slot.slotId,
        });
      } catch {
        setErrorDialog('Failed to assign player to slot.');
      }
    }
    setShowTrainingModal(false);
    setSelectedSlotIndex(null);
  };

  const handleUpgradeQuality = async () => {
    setShowUpgradeAlert(false);
    if (!manager?.gameworldId || !teamData?.teamId) return;
    try {
      const res = await callApi(
        `/${manager.gameworldId}/team/${teamData.teamId}/upgrade-training`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
        }
      );
      if (res?.trainingLevel) setTrainingGroundQuality(res.trainingLevel);
      if (typeof res?.balance === 'number') setTeamBalance(res.balance);
    } catch (error: any) {
      setErrorDialog(error?.message || 'Failed to upgrade training ground.');
      console.error('Failed to upgrade training ground:', error);
    }
  };

  if (loading || slotsLoading) {
    return (
      <Container>
        <ScrollView>
          <Header>
            <Skeleton height={28} width={180} radius={8} colorMode="light" />
            <Spacer height={12} />
            <QualityContainer>
              <QualityInfo>
                <Skeleton height={18} width={120} radius={6} colorMode="light" />
                <Spacer height={8} />
                <Skeleton height={22} width={80} radius={6} colorMode="light" />
                <Spacer height={8} />
                <Skeleton height={18} width={100} radius={6} colorMode="light" />
              </QualityInfo>
              <Skeleton height={40} width={120} radius={20} colorMode="light" />
            </QualityContainer>
          </Header>
          <SlotsContainer>
            <SectionTitle>Training Slots</SectionTitle>
            {[...Array(3)].map((_, i) => (
              <SkeletonSlotContainer key={i}>
                <SkeletonSlotContent>
                  <Skeleton height={16} width={100} radius={6} colorMode="light" />
                  <Spacer height={8} />
                  <Skeleton height={18} width={140} radius={6} colorMode="light" />
                  <Spacer height={8} />
                  <Skeleton height={14} width={90} radius={6} colorMode="light" />
                </SkeletonSlotContent>
                <ArrowContainer>
                  <MaterialIcons name="chevron-right" size={24} color={'#ccc'} />
                </ArrowContainer>
              </SkeletonSlotContainer>
            ))}
          </SlotsContainer>
        </ScrollView>
      </Container>
    );
  }

  const availablePlayers = teamData?.players || team?.players || [];

  return (
    <Container>
      <ScrollView>
        <Header>
          <HeaderTitle>Training Ground</HeaderTitle>

          <QualityContainer>
            <QualityInfo>
              <QualityLabel>Training Ground Quality</QualityLabel>
              <QualityValue>Level {trainingGroundQuality}</QualityValue>
              <QualityLabel>Balance: {formatPlayerValue(teamBalance)}</QualityLabel>
            </QualityInfo>

            <UpgradeButton onPress={() => setShowUpgradeAlert(true)}>
              <MaterialIcons name="upgrade" size={20} color="white" />
              <UpgradeButtonText>
                Upgrade ({formatPlayerValue(upgradeQualityCost)})
              </UpgradeButtonText>
            </UpgradeButton>
          </QualityContainer>
        </Header>

        <SlotsContainer>
          <SectionTitle>Training Slots</SectionTitle>

          {trainingSlots?.slots.map((slot: TrainingSlotData, index: number) => (
            <TrainingSlot
              key={index}
              slotIndex={index}
              isUnlocked={!slot.locked}
              unlockCost={slot.unlockCost}
              assignedPlayer={slot.player}
              trainingAttribute={slot.attribute}
              startValue={slot.startValue}
              currentValue={slot.currentValue}
              onPress={() => handleSlotPress(index)}
            />
          ))}
        </SlotsContainer>
      </ScrollView>

      {/* Training Assignment Modal */}
      <TrainingModal
        visible={showTrainingModal}
        players={availablePlayers}
        onClose={() => {
          setShowTrainingModal(false);
          setSelectedSlotIndex(null);
        }}
        onAssign={handleAssignPlayer}
      />

      {/* Unlock Slot Confirmation */}
      <CrossPlatformAlert
        visible={showUnlockAlert}
        title="Unlock Training Slot"
        message={`Unlock training slot ${(slotToUnlock || 0) + 1} for ${
          slotToUnlock !== null
            ? formatPlayerValue(trainingSlots?.slots[slotToUnlock]?.unlockCost || 0)
            : ''
        }?`}
        buttons={[
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              setShowUnlockAlert(false);
              setSlotToUnlock(null);
            },
          },
          {
            text: 'Unlock',
            onPress: handleUnlockSlot,
          },
        ]}
        onDismiss={() => {
          setShowUnlockAlert(false);
          setSlotToUnlock(null);
        }}
      />

      {/* Upgrade Quality Confirmation */}
      <CrossPlatformAlert
        visible={showUpgradeAlert}
        title="Upgrade Training Ground"
        message={`Upgrade training ground to level ${trainingGroundQuality + 1} for ${formatPlayerValue(upgradeQualityCost)}?`}
        buttons={[
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => setShowUpgradeAlert(false),
          },
          {
            text: 'Upgrade',
            onPress: handleUpgradeQuality,
          },
        ]}
        onDismiss={() => setShowUpgradeAlert(false)}
      />

      {/* Error Dialog */}
      <CrossPlatformAlert
        visible={!!errorDialog}
        title="Error"
        message={errorDialog || ''}
        buttons={[
          {
            text: 'OK',
            onPress: () => setErrorDialog(null),
          },
        ]}
        onDismiss={() => setErrorDialog(null)}
      />
    </Container>
  );
};

export default TrainingScreen;
