import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect } from 'react';
import { ActivityIndicator, FlatList, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../components/Text';
import { LAST_READ_MESSAGE_DATE_KEY, LAST_READ_MESSAGE_ID_KEY } from '../context/ManagerContext';
import { InboxMessage, useInboxMessages } from '../hooks/useInboxMessages';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const MessageCard = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  margin: 8px 16px;
  padding: 16px;
  border-radius: 8px;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

const MessageHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const MessageTitle = styled(Text)`
  font-size: 16px;
  font-family: 'NunitoBold';
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  flex: 1;
`;

const MessageDate = styled(Text)`
  font-size: 12px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
`;

const MessageCategory = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 4px 8px;
  border-radius: 12px;
  margin-bottom: 8px;
  align-self: flex-start;
`;

const MessageCategoryText = styled(Text)`
  font-size: 10px;
  color: white;
  font-family: 'NunitoBold';
  text-transform: uppercase;
`;

const MessageContent = styled.View`
  margin-top: 8px;
`;

const MessageText = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  line-height: 20px;
`;

const EmptyContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px;
`;

const EmptyText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
`;

const InboxScreen: React.FC = () => {
  const { data: messagesData, isLoading, error } = useInboxMessages();

  // Mark messages as read when the screen is opened
  useEffect(() => {
    const markMessagesAsRead = async () => {
      if (!messagesData?.messages || messagesData.messages.length === 0) {
        return;
      }

      try {
        // Get the most recent message
        const mostRecentMessage = messagesData.messages[0]; // Assuming messages are sorted by date descending

        // Store both the ID and date of the most recent message
        await AsyncStorage.setItem(LAST_READ_MESSAGE_ID_KEY, mostRecentMessage.id);
        await AsyncStorage.setItem(LAST_READ_MESSAGE_DATE_KEY, mostRecentMessage.date.toString());

        console.log('Marked messages as read up to:', mostRecentMessage.id, new Date(mostRecentMessage.date));
      } catch (err) {
        console.error('Error marking messages as read:', err);
      }
    };

    markMessagesAsRead();
  }, [messagesData]);

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const stripHtmlTags = (html: string) => {
    return html.replace(/<[^>]*>/g, '');
  };

  const renderMessage = ({ item }: { item: InboxMessage }) => (
    <MessageCard>
      <MessageHeader>
        <MessageTitle numberOfLines={1}>{item.extra.title}</MessageTitle>
        <MessageDate>{formatDate(item.date)}</MessageDate>
      </MessageHeader>

      <MessageCategory>
        <MessageCategoryText>{item.extra.category}</MessageCategoryText>
      </MessageCategory>

      <MessageContent>
        <MessageText>{stripHtmlTags(item.message)}</MessageText>
      </MessageContent>
    </MessageCard>
  );

  if (isLoading) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  if (error) {
    return (
      <Container>
        <EmptyContainer>
          <EmptyText>Failed to load messages. Please try again later.</EmptyText>
        </EmptyContainer>
      </Container>
    );
  }

  if (!messagesData?.messages || messagesData.messages.length === 0) {
    return (
      <Container>
        <EmptyContainer>
          <EmptyText>No messages yet. Check back later for updates!</EmptyText>
        </EmptyContainer>
      </Container>
    );
  }

  return (
    <Container>
      <FlatList
        data={messagesData.messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingVertical: 8 }}
      />
    </Container>
  );
};

export default InboxScreen;
