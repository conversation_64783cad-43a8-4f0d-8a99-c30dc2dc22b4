import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { PlayerLike } from '../../utils/PlayerUtils';
import {
  Card,
  CardContent,
  DetailButton,
} from './PlayerRowStyles';
import PlayerInfo from './PlayerInfo';

interface TapPlayerRowProps {
  player: PlayerLike;
  isSelected?: boolean;
  backgroundColor?: string;
  isActive?: boolean;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  onSelect?: (player: PlayerLike) => void;
  isSelectedForSwap?: boolean;
  onPlayerTap?: (playerId: string) => void;
}

// Swap icon component for tap mode
const SwapIcon: React.FC<{ isSelected: boolean; onPress: () => void }> = ({ isSelected, onPress }) => (
  <TouchableOpacity onPress={onPress} style={{ width: 60, alignItems: 'center', justifyContent: 'center' }}>
    <MaterialIcons 
      name="swap-vert" 
      size={24} 
      color={isSelected ? '#2196F3' : '#ffffff'} 
    />
  </TouchableOpacity>
);

const TapPlayerRow: React.FC<TapPlayerRowProps> = ({
  player,
  isSelected = false,
  backgroundColor,
  isActive = false,
  positionFilter = 'All',
  onSelect,
  isSelectedForSwap = false,
  onPlayerTap,
}) => {

  // Animation for selection in tap mode
  const scale = useSharedValue(1);
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  // Update animation when selection state changes
  React.useEffect(() => {
    if (isSelectedForSwap) {
      scale.value = withSpring(0.95);
    } else {
      scale.value = withSpring(1);
    }
  }, [isSelectedForSwap]);

  const handlePress = () => {
    if (onSelect) {
      onSelect(player);
    }
  };

  const handleSwapIconPress = () => {
    if (onPlayerTap) {
      onPlayerTap(player.playerId);
    }
  };

  // Determine background color based on injury and suspension status
  const isInjured = player.injuredUntil && player.injuredUntil > Date.now();
  const isSuspended = player.suspendedForGames > 0;
  let cardBackgroundColor = backgroundColor;
  if (isInjured || isSuspended) {
    cardBackgroundColor = '#e3172a'; // Light red for injured or suspended
  }

  return (
    <Animated.View style={animatedStyle}>
      <Card
        isSelected={isSelected || isSelectedForSwap}
        backgroundColor={cardBackgroundColor}
        isActive={isActive}
        isInSection={true}
      >
        <CardContent>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <SwapIcon isSelected={isSelectedForSwap} onPress={handleSwapIconPress} />
            <PlayerInfo player={player} positionFilter={positionFilter} />
          </View>
        </CardContent>

        {/* Detail button on the right side */}
        {onSelect && (
          <DetailButton onPress={handlePress}>
            <MaterialIcons name="chevron-right" size={24} color="#ffffff" />
          </DetailButton>
        )}
      </Card>
    </Animated.View>
  );
};

export default TapPlayerRow;
