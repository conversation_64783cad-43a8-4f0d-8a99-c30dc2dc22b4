import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { View } from 'react-native';
import { GestureDetector } from 'react-native-gesture-handler';
import { PlayerLike } from '../../utils/PlayerUtils';
import {
  Card,
  CardContent,
  DetailButton,
} from './PlayerRowStyles';
import PlayerInfo from './PlayerInfo';
import { DragHandle } from './SharedComponents';

interface DragPlayerRowProps {
  player: PlayerLike;
  isSelected?: boolean;
  backgroundColor?: string;
  isActive?: boolean;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  onSelect?: (player: PlayerLike) => void;
  dragGesture: any;
}

const DragPlayerRow: React.FC<DragPlayerRowProps> = ({
  player,
  isSelected = false,
  backgroundColor,
  isActive = false,
  positionFilter = 'All',
  onSelect,
  dragGesture,
}) => {
  const handlePress = () => {
    if (onSelect) {
      onSelect(player);
    }
  };

  // Determine background color based on injury and suspension status
  const isInjured = player.injuredUntil && player.injuredUntil > Date.now();
  const isSuspended = player.suspendedForGames > 0;
  let cardBackgroundColor = backgroundColor;
  if (isInjured || isSuspended) {
    cardBackgroundColor = '#e3172a'; // Light red for injured or suspended
  }

  return (
    <Card isSelected={isSelected} backgroundColor={cardBackgroundColor} isActive={isActive}>
      <GestureDetector gesture={dragGesture}>
        <CardContent>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <DragHandle />
            <PlayerInfo player={player} positionFilter={positionFilter} showImages={true} />
          </View>
        </CardContent>
      </GestureDetector>

      {/* Detail button on the right side */}
      {onSelect && (
        <DetailButton onPress={handlePress}>
          <MaterialIcons name="chevron-right" size={24} color="#ffffff" />
        </DetailButton>
      )}
    </Card>
  );
};

export default DragPlayerRow;
