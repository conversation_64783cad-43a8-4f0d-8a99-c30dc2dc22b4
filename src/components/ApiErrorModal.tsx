import { CrossPlatformAlert } from './CrossPlatformAlert';

interface ApiErrorModalProps {
  visible: boolean;
  error?: any;
  onDismiss: () => void;
}

export const ApiErrorModal = ({ visible, error, onDismiss }: ApiErrorModalProps) => {
  console.log('ApiErrorModal visible:', visible, 'error:', error);
  return (
    <CrossPlatformAlert
      visible={visible}
      title="Error"
      message={error?.message || 'An error occurred.'}
      buttons={[
        {
          text: 'OK',
          onPress: onDismiss,
        },
      ]}
      onDismiss={onDismiss}
    />
  );
};
