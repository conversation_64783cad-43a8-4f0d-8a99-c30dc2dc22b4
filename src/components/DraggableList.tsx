import React, { use<PERSON><PERSON>back, useEffect, useImperative<PERSON><PERSON><PERSON>, useRef, useState } from 'react';
import {
  Dimensions,
  LayoutChangeEvent,
  Platform,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

/**
 * DraggableList is a cross-platform reorderable list component that supports:
 * - Drag and drop reordering
 * - Smooth animations
 * - Auto-scrolling when dragging near edges
 * - Platform-specific optimizations for web and native
 *
 * The component uses:
 * - react-native-gesture-handler for drag detection
 * - react-native-reanimated for smooth animations
 * - Platform-specific scroll view implementations
 */

// Debug mode flag to enable console logging
const DEBUG = true;
const log = (message: string, ...args: any[]) => {
  if (DEBUG) console.log(`[DraggableList] ${message}`, ...args);
};

// Feature flag to enable/disable swap animations during reordering
const ENABLE_SWAP_ANIMATIONS = false;

// Configuration constants for auto-scrolling behavior
const AUTOSCROLL_THRESHOLD = 100; // Distance from edge to trigger auto-scroll
const AUTOSCROLL_SPEED = 5; // Scroll speed in pixels per frame
const PAGE_HEADER_OFFSET = 74; // Offset to account for header height

/**
 * Props interface for the DraggableList component
 * @template T - Generic type for the data items
 */
interface DraggableListProps<T> {
  data: T[]; // Array of items to render
  onReorder: (newData: T[]) => void; // Callback when items are reordered
  keyExtractor: (item: T) => string; // Function to get unique key for each item
  renderItem: (
    // Function to render each item
    info: {
      item: T;
      isDragging: boolean;
    },
    index: number
  ) => React.ReactNode;
  containerStyle?: any; // Optional container styling
}

// Type definition for the scroll view reference
// Handles both web and native scroll view implementations
type ScrollViewRef = {
  scrollTo: (params: { y: number; animated: boolean }) => void;
  scrollableRef: React.RefObject<HTMLDivElement | ScrollView>;
};

function DraggableList<T>({
  data,
  onReorder,
  keyExtractor,
  renderItem,
  containerStyle,
}: DraggableListProps<T>) {
  // State management for drag and drop functionality
  const [draggingIdx, setDraggingIdx] = useState<number | null>(null); // Currently dragged item index
  const [itemHeights, setItemHeights] = useState<number[]>([]); // Heights of each item
  const [itemOffsets, setItemOffsets] = useState<number[]>([]); // Y-offsets of each item
  const [isDragging, setIsDragging] = useState(false); // Global dragging state

  // Refs for maintaining component state and references
  const scrollViewRef = useRef<ScrollViewRef>(null); // Scroll view reference
  const scrollableRef = useRef<HTMLDivElement | ScrollView | null>(null); // Platform-specific scroll container
  const isMounted = useRef(true); // Component mount state
  const itemRefs = useRef<Array<any>>([]); // References to rendered items
  const isReordering = useRef(false); // Reordering state flag
  const dragLockRef = useRef(false); // Drag lock prevention

  // Reanimated shared values for animations
  const translateY = useSharedValue(0); // Global Y translation
  const itemTranslations = useRef(data.map(() => useSharedValue(0))).current; // Individual item translations

  // Auto-scroll state management
  const autoScrolling = useRef(false); // Auto-scroll active flag
  const scrollAnimation = useRef<number>(); // Animation frame reference

  // Add a helper function to stop auto-scrolling
  const stopAutoScroll = useCallback(() => {
    if (scrollAnimation.current) {
      cancelAnimationFrame(scrollAnimation.current);
      scrollAnimation.current = undefined;
    }
    autoScrolling.current = false;
  }, []);

  const getScrollOffset = useCallback((): number => {
    if (!scrollableRef.current) return 0;

    if (Platform.OS === 'web') {
      return (scrollableRef.current as HTMLDivElement).scrollTop;
    } else {
      return (scrollableRef.current as any)?._scrollMetrics?.offset || 0;
    }
  }, []);

  const endDrag = useCallback(() => {
    if (isMounted.current) {
      stopAutoScroll();
      setDraggingIdx(null);
      setIsDragging(false);
      isReordering.current = false;
      dragLockRef.current = false; // Reset the lock here too
    }
  }, [stopAutoScroll]);

  useEffect(() => {
    if (draggingIdx === null && itemHeights.length === data.length) {
      // Recalculate offsets when dragging finishes
      let currentOffset = 0;
      const offsets = itemHeights.map((height) => {
        const offset = currentOffset;
        currentOffset += height;
        return offset;
      });
      setItemOffsets(offsets);
    }
  }, [draggingIdx, itemHeights, data.length]);

  const performReorder = useCallback(
    (fromIndex: number, toIndex: number) => {
      log('Attempting reorder:', { fromIndex, toIndex });
      if (fromIndex === toIndex || fromIndex < 0 || toIndex < 0) return;

      isReordering.current = true;
      const currentScrollPosition = getScrollOffset();

      if (ENABLE_SWAP_ANIMATIONS) {
        // Calculate the distances for the swap
        const fromHeight = itemHeights[fromIndex];
        const toHeight = itemHeights[toIndex];

        if (toIndex > fromIndex) {
          itemTranslations[fromIndex].value = withSpring(toHeight);
          itemTranslations[toIndex].value = withSpring(-fromHeight);
        } else {
          itemTranslations[fromIndex].value = withSpring(-toHeight);
          itemTranslations[toIndex].value = withSpring(fromHeight);
        }

        // After animation completes, update the data and reset positions
        setTimeout(() => {
          const newData = [...data];
          const temp = newData[fromIndex];
          newData[fromIndex] = newData[toIndex];
          newData[toIndex] = temp;
          onReorder(newData);

          // Reset translations
          itemTranslations[fromIndex].value = 0;
          itemTranslations[toIndex].value = 0;
          translateY.value = 0;

          // Restore scroll position after state updates
          requestAnimationFrame(() => {
            if (Platform.OS === 'web') {
              (scrollableRef.current as HTMLDivElement).scrollTop = currentScrollPosition;
            } else {
              (scrollableRef.current as any)?.scrollTo({
                y: currentScrollPosition,
                animated: false,
              });
            }
          });

          endDrag();
        }, 300);
      } else {
        // Immediately swap without animations
        const newData = [...data];
        const temp = newData[fromIndex];
        newData[fromIndex] = newData[toIndex];
        newData[toIndex] = temp;
        onReorder(newData);
        log('Reorder complete, new data:', newData);

        translateY.value = 0;

        // Restore scroll position after state updates
        requestAnimationFrame(() => {
          if (Platform.OS === 'web') {
            (scrollableRef.current as HTMLDivElement).scrollTop = currentScrollPosition;
          } else {
            (scrollableRef.current as any)?.scrollTo({
              y: currentScrollPosition,
              animated: false,
            });
          }
        });

        endDrag();
      }
    },
    [data, onReorder, endDrag, translateY, itemHeights, itemTranslations, getScrollOffset]
  );

  const onItemLayout = useCallback((index: number, event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;
    setItemHeights((prev) => {
      if (prev[index] !== height) {
        const newHeights = [...prev];
        newHeights[index] = height;
        return newHeights;
      }
      return prev;
    });
    //log('Item layout update:', { index, height: event.nativeEvent.layout.height });
  }, []);

  const getHoverIndex = useCallback(
    (moveY: number): number => {
      if (draggingIdx === null || itemOffsets.length !== data.length) {
        return -1;
      }

      const scrollOffset = getScrollOffset();
      const absoluteY = moveY + scrollOffset;
      const adjustedY = absoluteY - PAGE_HEADER_OFFSET;

      log(`Position calculation:`, {
        moveY,
        scrollOffset,
        absoluteY,
        adjustedY,
        draggingIdx,
      });

      // Find the row where the adjusted point falls
      for (let i = 0; i < data.length; i++) {
        const itemTop = itemOffsets[i];
        const itemHeight = itemHeights[i] || 0;
        const itemBottom = itemTop + itemHeight;

        if (adjustedY >= itemTop && adjustedY < itemBottom) {
          log(`Selected row ${i}`);
          return i;
        }
      }

      // Handle edge cases
      if (adjustedY < itemOffsets[0]) {
        return 0;
      }
      if (adjustedY >= itemOffsets[itemOffsets.length - 1] + itemHeights[itemHeights.length - 1]) {
        return data.length - 1;
      }

      return draggingIdx; // Fallback to current position
    },
    [draggingIdx, data.length, itemOffsets, itemHeights, getScrollOffset]
  );

  const finalizeMove = useCallback(
    (moveY: number) => {
      try {
        stopAutoScroll();
        const hoverIdx = getHoverIndex(moveY);
        if (hoverIdx !== -1 && hoverIdx !== draggingIdx && draggingIdx !== null) {
          performReorder(draggingIdx, hoverIdx);
        } else {
          endDrag();
        }
      } catch (error) {
        console.log('Error in finalizing move:', error);
        stopAutoScroll();
        endDrag();
      }
    },
    [getHoverIndex, draggingIdx, performReorder, endDrag, stopAutoScroll]
  );

  useEffect(() => {
    if (itemHeights.length !== data.length) {
      setItemHeights(Array(data.length).fill(0));
      itemRefs.current = Array(data.length).fill(null);
    }
  }, [data.length]);

  useEffect(() => {
    if (itemHeights.length === data.length && itemHeights.every((h) => h > 0)) {
      let currentOffset = 0;
      const offsets = itemHeights.map((height) => {
        const offset = currentOffset;
        currentOffset += height;
        return offset;
      });
      setItemOffsets(offsets);
    }
  }, [itemHeights, data.length]);

  // Add this function to handle auto-scrolling
  const handleAutoScroll = useCallback(
    (y: number) => {
      if (draggingIdx === null || !scrollableRef.current) return;

      // Get current scroll position and view dimensions
      let scrollY = 0;
      let layoutHeight = Dimensions.get('window').height;
      let scrollHeight = 0;

      // Handle both web and native platforms
      if (Platform.OS === 'web') {
        const element = scrollableRef.current as HTMLDivElement;
        scrollY = element.scrollTop;
        layoutHeight = element.clientHeight;
        scrollHeight = element.scrollHeight;
      } else {
        const scrollView = scrollableRef.current as any;
        scrollY = scrollView?._scrollMetrics?.offset || 0;
        layoutHeight = scrollView?._scrollMetrics?.layoutMeasurement || 0;
        scrollHeight = scrollView?._scrollMetrics?.contentLength || 0;
      }

      const touchY = y - PAGE_HEADER_OFFSET;
      const topThreshold = AUTOSCROLL_THRESHOLD;
      const bottomThreshold = layoutHeight - AUTOSCROLL_THRESHOLD;

      let scrollAmount = 0;

      if (touchY < topThreshold) {
        scrollAmount = -AUTOSCROLL_SPEED;
      } else if (touchY > bottomThreshold) {
        scrollAmount = AUTOSCROLL_SPEED;
      }

      if (scrollAmount !== 0) {
        const newOffset = Math.max(
          0,
          Math.min(scrollY + scrollAmount, scrollHeight - layoutHeight)
        );

        // Handle scrolling for both web and native
        if (Platform.OS === 'web') {
          const element = scrollableRef.current as HTMLDivElement;
          element.scrollTop = newOffset;
        } else {
          (scrollableRef.current as any)?.scrollTo({ y: newOffset, animated: false });
        }

        if (!autoScrolling.current) {
          autoScrolling.current = true;
          scrollAnimation.current = requestAnimationFrame(() => {
            handleAutoScroll(y);
          });
        }
      } else {
        stopAutoScroll();
      }
    },
    [draggingIdx]
  );

  useEffect(() => {
    return () => {
      isMounted.current = false;
      stopAutoScroll();
    };
  }, [stopAutoScroll]);

  const createPanGesture = (index: number) => {
    let startScrollY = 0;
    const activeIndexRef = useRef<number | null>(null);
    const isActive = useSharedValue(false);

    return Gesture.Pan()
      .minDistance(5)
      .shouldCancelWhenOutside(false)
      .onBegin(() => {
        translateY.value = 0;
        isActive.value = true;

        // Set states synchronously before any animations
        runOnJS(setDraggingIdx)(index);
        runOnJS(setIsDragging)(true);

        log('Gesture begin', { index });
      })
      .onStart(() => {
        log('Gesture start', { index });
        activeIndexRef.current = index;
        startScrollY = getScrollOffset();
      })
      .onUpdate((event) => {
        if (!isActive.value) return;

        const currentScrollY = getScrollOffset();
        const scrollDelta = currentScrollY - startScrollY;
        const newTranslateY = event.translationY + scrollDelta;

        // Use withSpring for smoother animation
        translateY.value = withSpring(newTranslateY, {
          damping: 50,
          stiffness: 400,
        });

        log('Gesture update', {
          index,
          translationY: event.translationY,
          newTranslateY,
          isActive: isActive.value,
        });

        runOnJS(handleAutoScroll)(event.absoluteY);
      })
      .onFinalize((event) => {
        log('Gesture finalize', { index });
        isActive.value = false;
        translateY.value = withSpring(0);

        if (activeIndexRef.current === index) {
          runOnJS(stopAutoScroll)();
          runOnJS(finalizeMove)(event.absoluteY);
        }

        activeIndexRef.current = null;

        // Reset states after animation
        runOnJS(setDraggingIdx)(null);
        runOnJS(setIsDragging)(false);
      });
  };

  // Create a custom scroll view component that forwards the ref
  const CustomScrollView = React.forwardRef<ScrollViewRef, any>((props, ref) => {
    const innerRef = useRef<ScrollView | null>(null);

    useImperativeHandle(ref, () => ({
      scrollTo: (params: { y: number; animated: boolean }) => {
        innerRef.current?.scrollTo(params);
      },
      scrollableRef: innerRef,
    }));

    return (
      <ScrollView
        {...props}
        ref={(r) => {
          if (r) {
            // For web, we need to access the underlying DOM element
            if (Platform.OS === 'web') {
              const webScrollElement = (r as any).getScrollableNode?.();
              if (webScrollElement) {
                scrollableRef.current = webScrollElement;
              }
            } else {
              scrollableRef.current = r;
            }

            // Set the inner ref separately
            if (innerRef && 'current' in innerRef) {
              innerRef.current = r;
            }
          }
        }}
        onScrollBeginDrag={(e) => {
          if (!isDragging) {
            props.onScrollBeginDrag?.(e);
          }
        }}
        scrollEnabled={!isDragging} // Disable native scroll while dragging
        maintainVisibleContentPosition={{
          // This helps maintain scroll position
          minIndexForVisible: 0,
          autoscrollToTopThreshold: null,
        }}
      />
    );
  });

  // ScrollView setup
  return (
    <GestureHandlerRootView style={[styles.container, containerStyle]}>
      {/* ScrollView wrapper enables scrolling when not dragging */}
      <CustomScrollView
        ref={scrollViewRef}
        scrollEnabled={!isDragging}
        contentContainerStyle={{
          minHeight: Math.max(
            itemHeights.reduce((sum, height) => sum + height, 0),
            Dimensions.get('window').height / 2
          ),
        }}
        scrollEventThrottle={16} // Ensures smooth scrolling
        onScrollBeginDrag={stopAutoScroll}
        style={styles.scrollView}
      >
        {/* Main list container */}
        <View style={styles.listContainer}>
          {/* Map through items and render each one */}
          {data.map((item: T, index: number) => {
            const isBeingDragged = index === draggingIdx;

            const itemStyle = useAnimatedStyle(() => {
              'worklet';

              const translation = isBeingDragged ? translateY.value : itemTranslations[index].value;
              const zIndex = isBeingDragged ? 999 : 1;

              log('Animated style calculation', {
                index,
                isBeingDragged,
                translation,
                zIndex,
              });

              return {
                transform: [{ translateY: translation }],
                zIndex,
              };
            }, [isBeingDragged, index]);

            return (
              <Animated.View
                key={keyExtractor(item)}
                style={[styles.itemContainer, isBeingDragged && { zIndex: 100 }, itemStyle]}
                ref={(ref) => {
                  itemRefs.current[index] = ref;
                }}
                onLayout={(e) => onItemLayout(index, e)}
              >
                <GestureDetector gesture={createPanGesture(index)}>
                  <Animated.View style={[styles.row, isBeingDragged && styles.dragging]}>
                    {renderItem(
                      {
                        item,
                        isDragging: isBeingDragged,
                      },
                      index
                    )}
                  </Animated.View>
                </GestureDetector>
              </Animated.View>
            );
          })}
        </View>
      </CustomScrollView>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
    width: '100%',
  },
  container: {
    flex: 1,
    width: '100%',
  },
  listContainer: {
    position: 'relative',
    width: '100%',
  },
  itemContainer: {
    width: '100%',
  },
  row: {
    width: '100%',
  },
  dragging: {
    zIndex: 999,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
  },
});

export default DraggableList;
