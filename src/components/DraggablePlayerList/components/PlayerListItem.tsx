/**
 * PlayerListItem Component
 *
 * This component renders an individual player item in the draggable list.
 * It uses styled-components for styling and react-native-gesture-handler
 * for handling drag gestures. The component is animated using react-native-reanimated.
 */

import React from 'react';
import Animated from 'react-native-reanimated';
import styled from 'styled-components/native';
import TeamPlayerRow from '../../PlayerRow';
import { PLAYER_CARD_HEIGHT } from '../constants';
import { useGesture } from '../hooks/useGesture';
import { TPlayerListItem } from '../types';

// Styled components for the PlayerListItem
const AnimatedContainer = styled(Animated.View)`
  height: ${PLAYER_CARD_HEIGHT}px;
  position: absolute; /* Absolute positioning allows items to overlap during drag */
  width: 100%; /* Full width to ensure consistent sizing */
`;

/**
 * PlayerListItem Component
 *
 * This component renders a single player in the draggable list.
 * It receives various props related to dragging state and positioning.
 */
export const PlayerListItem = ({
  item,
  isDragging,
  draggedItemId,
  currentPlayerPositions,
  scrollUp,
  scrollDown,
  scrollY,
  isDragInProgress,
  index,
  onSelect,
  selectedPlayerId,
  positionFilter,
  teamAverages,
  inputMethod = 'drag',
  isSelectedForSwap = false,
  onPlayerTap,
}: TPlayerListItem) => {
  // Get the animated styles and gesture handler from the useGesture hook
  const { animatedStyles, gesture } = useGesture(
    item,
    isDragging,
    draggedItemId,
    currentPlayerPositions,
    scrollUp,
    scrollDown,
    scrollY,
    isDragInProgress
  );

  // Get background color based on player position in the list
  const getBackgroundColor = (index: number) => {
    if (index === 0) return '#ffeb3b40';
    if (index >= 1 && index <= 4) return '#4caf5040';
    if (index >= 5 && index <= 8) return '#2196f340';
    if (index >= 9 && index <= 10) return '#f4433640';
    if (index >= 11 && index <= 15) return '#9c27b040';
    return '#9e9e9e40';
  };

  return (
    // The container uses animatedStyles from the useGesture hook
    <AnimatedContainer style={animatedStyles}>
      {/*<Animated.View>*/}
      <TeamPlayerRow
        player={item}
        backgroundColor={getBackgroundColor(index)}
        isActive={draggedItemId.value === item.playerId}
        isSelected={selectedPlayerId === item.playerId}
        onSelect={onSelect}
        dragGesture={inputMethod === 'drag' ? gesture : undefined}
        positionFilter={positionFilter}
        inputMethod={inputMethod}
        isSelectedForSwap={isSelectedForSwap}
        onPlayerTap={onPlayerTap}
      />
      {/*</Animated.View>*/}
    </AnimatedContainer>
  );
};
