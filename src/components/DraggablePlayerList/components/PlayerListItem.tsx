/**
 * PlayerListItem Component
 *
 * This component renders an individual player item in the draggable list.
 * It uses styled-components for styling and react-native-gesture-handler
 * for handling drag gestures. The component is animated using react-native-reanimated.
 */

import React from 'react';
import Animated from 'react-native-reanimated';
import styled from 'styled-components/native';
import DragPlayerRow from '../../PlayerRow/DragPlayerRow';
import TapPlayerRow from '../../PlayerRow/TapPlayerRow';
import { PLAYER_CARD_HEIGHT } from '../constants';
import { useGesture } from '../hooks/useGesture';
import { TPlayerListItem } from '../types';

// Styled components for the PlayerListItem
const AnimatedContainer = styled(Animated.View)<{ inputMethod?: string }>`
  height: ${PLAYER_CARD_HEIGHT}px;
  position: ${(props) => (props.inputMethod === 'tap' ? 'relative' : 'absolute')};
  width: 100%; /* Full width to ensure consistent sizing */
`;

/**
 * PlayerListItem Component
 *
 * This component renders a single player in the draggable list.
 * It receives various props related to dragging state and positioning.
 */
export const PlayerListItem = ({
  item,
  isDragging,
  draggedItemId,
  currentPlayerPositions,
  scrollUp,
  scrollDown,
  scrollY,
  isDragInProgress,
  index,
  onSelect,
  selectedPlayerId,
  positionFilter,
  teamAverages,
  inputMethod = 'tap',
  isSelectedForSwap = false,
  onPlayerTap,
}: TPlayerListItem) => {
  // Get the animated styles and gesture handler from the useGesture hook
  const { animatedStyles, gesture } = useGesture(
    item,
    isDragging,
    draggedItemId,
    currentPlayerPositions,
    scrollUp,
    scrollDown,
    scrollY,
    isDragInProgress
  );

  // Use neutral background color for all players (unless they're injured/suspended)
  const getBackgroundColor = () => {
    return '#9e9e9e40'; // Neutral gray background for all players
  };

  return (
    // The container uses animatedStyles from the useGesture hook
    <AnimatedContainer style={inputMethod === 'drag' ? animatedStyles : undefined} inputMethod={inputMethod}>
      {inputMethod === 'drag' ? (
        <DragPlayerRow
          player={item}
          backgroundColor={getBackgroundColor()}
          isActive={draggedItemId.value === item.playerId}
          isSelected={selectedPlayerId === item.playerId}
          onSelect={onSelect}
          dragGesture={gesture}
          positionFilter={positionFilter}
        />
      ) : (
        <TapPlayerRow
          player={item}
          backgroundColor={getBackgroundColor()}
          isActive={draggedItemId.value === item.playerId}
          isSelected={selectedPlayerId === item.playerId}
          onSelect={onSelect}
          positionFilter={positionFilter}
          isSelectedForSwap={isSelectedForSwap}
          onPlayerTap={onPlayerTap}
        />
      )}
    </AnimatedContainer>
  );
};
