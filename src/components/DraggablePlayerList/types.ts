import { SharedValue } from 'react-native-reanimated';
import { TeamInputMethod } from '../../context/TeamInputMethodContext';
import { Player } from '../../models/player';

export type TPlayerItem = Player;

export type TPlayerListItem = {
  item: TPlayerItem;
  isDragging: SharedValue<0 | 1>;
  draggedItemId: SharedValue<NullableString>;
  currentPlayerPositions: SharedValue<TPlayerPositions>;
  scrollUp: () => void;
  scrollDown: () => void;
  scrollY: SharedValue<number>;
  isDragInProgress: SharedValue<boolean>;
  index: number;
  onSelect?: (player: TPlayerItem) => void;
  selectedPlayerId?: string;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  teamAverages?: Record<string, number>;
  inputMethod?: TeamInputMethod;
  isSelectedForSwap?: boolean;
  onPlayerTap?: (playerId: string) => void;
};

export type TPlayerPositions = {
  [key: string]: {
    updatedIndex: number;
    updatedTop: number;
  };
};

export type NullableNumber = null | number;
export type NullableString = null | string;
