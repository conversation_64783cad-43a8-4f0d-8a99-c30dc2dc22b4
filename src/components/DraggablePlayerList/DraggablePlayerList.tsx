/**
 * DraggablePlayerList Component
 *
 * This is the main component that renders a draggable, reorderable list of players.
 * It uses react-native-reanimated for smooth animations and gesture handling.
 *
 * The list allows users to drag items to reorder them, with automatic scrolling
 * when dragging near the edges of the list.
 */

import React, { useEffect, useState } from 'react';
import { Dimensions } from 'react-native';

import {
  runOnJS,
  scrollTo,
  useAnimatedReaction,
  useAnimatedRef,
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import { TeamInputMethod } from '../../context/TeamInputMethodContext';
import { Player } from '../../models/player';
import { AnimatedScrollView, ListContainer } from './DraggablePlayerList.styles';
import { PlayerListItem } from './components/PlayerListItem';
import {
  getInitialPositions,
  PLAYER_CARD_HEIGHT,
  SCREEN_HEIGHT,
  SCROLL_SPEED_OFFSET,
} from './constants';
import { NullableString, TPlayerPositions } from './types';

interface DraggablePlayerListProps {
  data: Player[];
  onReorder: (newData: Player[]) => void;
  onSelect?: (player: Player) => void;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  teamAverages?: Record<string, number>;
  inputMethod?: TeamInputMethod;
}

/**
 * DraggablePlayerList Component
 *
 * This component manages the state and interactions for the draggable player list.
 */
export const DraggablePlayerList: React.FC<DraggablePlayerListProps> = ({
  data,
  onReorder,
  onSelect,
  positionFilter = 'All',
  teamAverages = {},
  inputMethod = 'tap',
}) => {
  console.log('data', data);
  // Reference to the scroll view for programmatic scrolling
  const scrollviewRef = useAnimatedRef();

  // Shared value that tracks the position of each player in the list
  const currentPlayerPositions = useSharedValue<TPlayerPositions>(getInitialPositions(data));

  // Controls the visual state of dragging animations (0 = not dragging, 1 = dragging)
  const isDragging = useSharedValue<0 | 1>(0);

  // State for tap mode - track selected players for swapping
  const [selectedPlayers, setSelectedPlayers] = useState<string[]>([]);

  // Handle player selection in tap mode
  const handlePlayerTap = (playerId: string) => {
    if (inputMethod !== 'tap') return;

    setSelectedPlayers((prev) => {
      if (prev.includes(playerId)) {
        // Deselect if already selected
        return prev.filter((id) => id !== playerId);
      } else if (prev.length === 0) {
        // First selection
        return [playerId];
      } else if (prev.length === 1) {
        // Second selection - perform swap
        const firstPlayerId = prev[0];
        const secondPlayerId = playerId;

        // Find indices of the players to swap
        const firstIndex = data.findIndex((p) => p.playerId === firstPlayerId);
        const secondIndex = data.findIndex((p) => p.playerId === secondPlayerId);

        if (firstIndex !== -1 && secondIndex !== -1) {
          // Create new array with swapped players
          const newData = [...data];
          [newData[firstIndex], newData[secondIndex]] = [newData[secondIndex], newData[firstIndex]];
          onReorder(newData);
        }

        // Clear selection after swap
        return [];
      } else {
        // Should not happen, but reset to single selection
        return [playerId];
      }
    });
  };

  // Stores the ID of the item currently being dragged (null when not dragging)
  const draggedItemId = useSharedValue<NullableString>(null);

  // Flag to track if dragging is in progress, used to control auto-scrolling
  const isDragInProgress = useSharedValue(false);

  // Tracks the current scroll position of the list
  const scrollY = useSharedValue(0);

  // Update positions when data changes
  useEffect(() => {
    currentPlayerPositions.value = getInitialPositions(data);
  }, [data]);

  /**
   * scrollUp - Worklet function to scroll the list upward
   *
   * Called when dragging near the top edge of the visible area.
   */
  const scrollUp = () => {
    'worklet';
    const newY = Math.max(0, scrollY.value - SCROLL_SPEED_OFFSET);
    console.log('Scrolling up', { current: scrollY.value, new: newY });
    scrollTo(scrollviewRef, 0, newY, false);
  };

  /**
   * scrollDown - Worklet function to scroll the list downward
   *
   * Called when dragging near the bottom edge of the visible area.
   */
  const scrollDown = () => {
    'worklet';
    const maxScroll = data.length * PLAYER_CARD_HEIGHT - SCREEN_HEIGHT;
    const newY = Math.min(maxScroll, scrollY.value + SCROLL_SPEED_OFFSET);
    console.log('Scrolling down', { current: scrollY.value, new: newY });
    scrollTo(scrollviewRef, 0, newY, false);
  };

  /**
   * scrollHandler - Tracks scroll position changes
   *
   * This animated scroll handler updates the scrollY shared value
   * whenever the user scrolls the list manually.
   */
  const scrollHandler = useAnimatedScrollHandler((event) => {
    scrollY.value = event.contentOffset.y;
  });

  // Get screen width for consistent sizing across the app
  const { width: SCREEN_WIDTH } = Dimensions.get('window');

  // Function to handle reordering when drag ends
  const handleReorder = () => {
    // Create a new array with the updated order
    const newOrder = [...data];

    // Sort the array based on the current positions
    newOrder.sort((a, b) => {
      const posA = currentPlayerPositions.value[a.playerId]?.updatedIndex || 0;
      const posB = currentPlayerPositions.value[b.playerId]?.updatedIndex || 0;
      return posA - posB;
    });

    // Call the onReorder callback with the new order
    onReorder(newOrder);
  };

  useAnimatedReaction(
    () => draggedItemId.value,
    (currentId, previousId) => {
      if (currentId === null && previousId !== null) {
        // This runs on the JS thread, so we need to use runOnJS
        runOnJS(handleReorder)();
      }
    },
    [handleReorder]
  );

  return (
    <ListContainer>
      {/*
        AnimatedScrollView handles scrolling and contains all list items
        - scrollEventThrottle: Controls how frequently scroll events are fired
        - ref: Used for programmatic scrolling during drag operations
        - onScroll: Updates the scrollY shared value when scrolling
      */}
      <AnimatedScrollView
        scrollEventThrottle={10}
        ref={scrollviewRef}
        onScroll={scrollHandler}
        contentContainerStyle={{
          height: data.length * PLAYER_CARD_HEIGHT, // Total height based on number of items
          width: '100%', // Full width for consistent sizing
        }}
        style={{ width: '100%' }} // Ensure the scroll view itself is full width
      >
        {/* Map through the player data and render a PlayerListItem for each */}
        {data.map((player, index) => {
          console.log(player.playerId, data.find((p) => onSelect && p === data[index])?.playerId);
          const isSelectedForSwap = selectedPlayers.includes(player.playerId);
          return (
            <PlayerListItem
              key={player.playerId}
              item={player}
              isDragging={isDragging}
              draggedItemId={draggedItemId}
              currentPlayerPositions={currentPlayerPositions}
              scrollUp={scrollUp}
              scrollDown={scrollDown}
              scrollY={scrollY}
              isDragInProgress={isDragInProgress}
              index={index}
              onSelect={onSelect}
              selectedPlayerId={data.find((p) => onSelect && p === data[index])?.playerId}
              positionFilter={positionFilter}
              teamAverages={teamAverages}
              inputMethod={inputMethod}
              isSelectedForSwap={isSelectedForSwap}
              onPlayerTap={handlePlayerTap}
            />
          );
        })}
      </AnimatedScrollView>
    </ListContainer>
  );
};
