// Native-specific barrel file for BannerAd
// This file will be used when bundling for native platforms (Android/iOS)

import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { BannerAdSize, BannerAd as MobileBannerAd, TestIds } from 'react-native-google-mobile-ads';
import { SafeAreaView } from 'react-native-safe-area-context';

// Use test ad unit ID in development, and real ad unit ID in production
const adUnitId = __DEV__ ? TestIds.BANNER : 'ca-app-pub-3959534729713487/1389516945';

export const BannerAd: React.FC = () => {
  // Initialize the Mobile Ads SDK
  useEffect(() => {
    const MobileAds = require('react-native-google-mobile-ads');
    MobileAds.MobileAds()
      .initialize()
      .then(() => {
        console.log('Mobile Ads SDK initialized successfully');
      })
      .catch((error: any) => {
        console.error('Mobile Ads SDK initialization failed:', error);
      });
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <MobileBannerAd
        unitId={adUnitId}
        size={BannerAdSize.BANNER}
        requestOptions={{
          requestNonPersonalizedAdsOnly: true,
        }}
        onAdLoaded={() => {
          console.log('Ad loaded');
        }}
        onAdFailedToLoad={(error) => {
          console.error('Ad failed to load: ', error);
        }}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    backgroundColor: 'transparent',
    paddingVertical: 5,
  },
});

export default BannerAd;
