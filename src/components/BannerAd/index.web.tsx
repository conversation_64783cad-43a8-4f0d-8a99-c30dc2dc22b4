// Web-specific barrel file for BannerAd
// This file will be used when bundling for web platform
// It explicitly avoids importing any native-only modules

import React from 'react';
import { StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Web version of BannerAd - doesn't use react-native-google-mobile-ads
export const BannerAd: React.FC = () => {
  // Return an empty container with the same styling
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.adPlaceholder}>
        <div>Ad Placeholder</div>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    backgroundColor: 'transparent',
    paddingVertical: 5,
  },
  adPlaceholder: {
    // Match the size of a standard banner ad
    width: 320,
    height: 50,
  },
});

export default BannerAd;
