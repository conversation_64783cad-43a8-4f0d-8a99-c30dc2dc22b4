import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect } from 'react';
import {
  cancelAllMatchNotifications,
  configureNotifications,
  scheduleAllMatchNotifications,
} from '../services/NotificationScheduler';

/**
 * Hook to manage notification scheduling based on user preferences
 */
export const useNotificationScheduler = () => {
  useEffect(() => {
    // Configure notification behavior when the hook mounts
    configureNotifications();
  }, []);

  /**
   * Update notification scheduling based on current preferences
   */
  const updateNotificationScheduling = async () => {
    try {
      // Check if pre-match push notifications are enabled
      const preMatchPushEnabled = await AsyncStorage.getItem('@notification_preMatch_push');
      
      if (preMatchPushEnabled === 'true') {
        console.log('Pre-match notifications enabled, scheduling notifications');
        await scheduleAllMatchNotifications();
      } else {
        console.log('Pre-match notifications disabled, cancelling notifications');
        await cancelAllMatchNotifications();
      }
    } catch (error) {
      console.error('Error updating notification scheduling:', error);
    }
  };

  return {
    updateNotificationScheduling,
  };
};
