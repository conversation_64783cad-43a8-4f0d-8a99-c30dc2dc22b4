import debounce from 'lodash.debounce';
import { useCallback, useState } from 'react';
import { callApi } from '../api/client';
import { useManager } from '../context/ManagerContext';
import { Player } from '../models/player';

/**
 * Hook for updating player order with API
 *
 * Features:
 * - Debounced API calls to prevent spamming
 * - Loading state tracking
 * - Error handling
 */
export function useUpdatePlayerOrder() {
  const [isSaving, setIsSaving] = useState(false);
  const { manager } = useManager();

  // Create a memoized debounced function
  const debouncedUpdateOrder = useCallback(
    debounce(async (playerIds: string[]) => {
      if (!manager?.gameworldId || !manager?.team) return;

      setIsSaving(true);
      try {
        await callApi(`/${manager.gameworldId}/team/${manager.team.teamId}`, {
          method: 'POST',
          body: JSON.stringify({ playerIds: playerIds }),
        });
      } catch (error) {
        console.error('Failed to update player order:', error);
        // You might want to add error handling here
      } finally {
        setIsSaving(false);
      }
    }, 1000), // 1 second debounce
    [manager?.gameworldId, manager?.team]
  );

  // Function to call when player order changes
  const updatePlayerOrder = useCallback(
    (players: Player[]) => {
      const playerIds = players.map((player) => player.playerId);
      debouncedUpdateOrder(playerIds);
    },
    [debouncedUpdateOrder]
  );

  return { updatePlayerOrder, isSaving };
}
