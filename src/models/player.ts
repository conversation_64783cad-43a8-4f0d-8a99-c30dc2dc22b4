export interface Attributes {
  // goalkeeper
  reflexes: number;
  positioning: number;
  shotStopping: number;
  // defender
  tackling: number;
  marking: number;
  heading: number;
  // attacker
  finishing: number;
  pace: number;
  crossing: number;
  // midfielder
  passing: number;
  vision: number;
  ballControl: number;

  stamina: number;
}

export interface Player {
  gameworldId: string;
  firstName: string;
  surname: string;
  teamId: string;
  attributes: Attributes;
  value: number;
  age: number;
  playerId: string;
  energy: number; // 0-100
  lastMatchPlayed: number;
  injuredUntil?: number;
  suspendedForGames: number;
  retiringAtEndOfSeason: boolean;
}

export function calculateBestPosition(player: Player): string[] {
  const attributes = player.attributes;
  const attributeValues = Object.values(attributes);
  const average = attributeValues.reduce((sum, value) => sum + value, 0) / attributeValues.length;

  const positions: string[] = [];

  const gkAttributes = (attributes.reflexes + attributes.positioning + attributes.shotStopping) / 3;
  const dfAttributes = (attributes.tackling + attributes.marking + attributes.heading) / 3;
  const cmAttributes = (attributes.passing + attributes.vision + attributes.ballControl) / 3;

  const attributeScores = [
    { position: 'GK', score: gkAttributes },
    { position: 'DF', score: dfAttributes },
    { position: 'FWD', score: attributes.finishing },
    { position: 'WM', score: attributes.crossing },
    { position: 'CM', score: cmAttributes },
  ];

  const bestScore = Math.max(...attributeScores.map((attr) => attr.score));

  attributeScores.forEach((attr) => {
    if (attr.score >= average + 5 && attr.score === bestScore) {
      positions.push(attr.position);
    }
  });

  if (positions.length === 0) {
    const bestPosition = attributeScores.find((attr) => attr.score === bestScore);
    if (bestPosition) {
      positions.push(bestPosition.position);
    }
  }

  return positions;
}
