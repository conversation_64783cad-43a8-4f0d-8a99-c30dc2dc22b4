// Define notification categories
import { Team } from './team';

export enum NotificationCategory {
  TRANSFERS = 'transfers',
  TRAINING = 'training',
  PRE_MATCH = 'preMatch',
  SCOUTING_RESULTS = 'scoutingResults',
  POST_MATCH = 'postMatch',
  SYSTEM_ANNOUNCEMENTS = 'announcements',
}

// Define notification channels
export enum NotificationChannel {
  PUSH = 'push',
  EMAIL = 'email',
}

// Notification preferences type
export type NotificationPreferences = {
  [key in NotificationCategory]?: {
    [channel in NotificationChannel]?: boolean;
  };
};

export interface Manager {
  managerId: string;
  createdAt: number;
  lastActive: number;
  firstName?: string;
  lastName?: string;
  team?: Team;
  gameworldId?: string;
  scoutTokens: number;
  superScoutTokens: number;
  notificationPreferences?: NotificationPreferences;
}
