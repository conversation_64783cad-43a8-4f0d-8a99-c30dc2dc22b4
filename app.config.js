export default ({ config }) => ({
  expo: {
    name: 'Jumpers For Goalposts',
    slug: 'JumpersForGoalpostsNative',
    scheme: 'jfg',
    version: '1.0.0',
    orientation: 'default', //'portrait',
    icon: './assets/icon.png',
    userInterfaceStyle: 'light',
    newArchEnabled: true,
    jsEngine: 'hermes',
    plugins: [
      [
        'expo-font',
        {
          fonts: [
            'node_modules/@expo-google-fonts/nunito/Nunito_400Regular_Italic.ttf',
            'node_modules/@expo-google-fonts/nunito/Nunito_700Bold.ttf',
          ],
        },
      ],
      [
        'expo-notifications',
        {
          icon: './assets/icon.png',
          color: '#ffffff',
          defaultChannel: 'default',
        },
      ],
      'expo-router',
      [
        '@sentry/react-native/expo',
        {
          url: 'https://sentry.io/',
          project: 'jumpers-for-goalposts',
          organization: 'rwsoftware',
        },
      ],
      [
        'react-native-google-mobile-ads',
        {
          androidAppId: 'ca-app-pub-3959534729713487~9675201071',
          // Adding dummy iosAppId to silence the warning - no iOS app is being developed
          iosAppId: 'ca-app-pub-0000000000000000~0000000000',
          skAdNetworkItems: [
            'cstr6suwn9.skadnetwork',
            '4fzdc2evr5.skadnetwork',
            '2fnua5tdw4.skadnetwork',
            'ydx93a7ass.skadnetwork',
            'p78axxw29g.skadnetwork',
            'v72qych5uu.skadnetwork',
            'ludvb6z3bs.skadnetwork',
            'cp8zw746q7.skadnetwork',
            '3sh42y64q3.skadnetwork',
            'c6k4g5qg8m.skadnetwork',
            's39g8k73mm.skadnetwork',
            '3qy4746246.skadnetwork',
            'f38h382jlk.skadnetwork',
            'hs6bdukanm.skadnetwork',
            'mlmmfzh3r3.skadnetwork',
            'v4nxqhlyqp.skadnetwork',
            'wzmmz9fp6w.skadnetwork',
            'su67r6k2v3.skadnetwork',
            'yclnxrl5pm.skadnetwork',
            't38b2kh725.skadnetwork',
            '7ug5zh24hu.skadnetwork',
            'gta9lk7p23.skadnetwork',
            'vutu7akeur.skadnetwork',
            'y5ghdn5j9k.skadnetwork',
            'v9wttpbfk9.skadnetwork',
            'n38lu8286q.skadnetwork',
            '47vhws6wlr.skadnetwork',
            'kbd757ywx3.skadnetwork',
            '9t245vhmpl.skadnetwork',
            'a2p9lx4jpn.skadnetwork',
            '22mmun2rn5.skadnetwork',
            '44jx6755aq.skadnetwork',
            'k674qkevps.skadnetwork',
            '4468km3ulz.skadnetwork',
            '2u9pt9hc89.skadnetwork',
            '8s468mfl3y.skadnetwork',
            'klf5c3l5u5.skadnetwork',
            'ppxm28t8ap.skadnetwork',
            'kbmxgpxpgc.skadnetwork',
            'uw77j35x4d.skadnetwork',
            '578prtvx9j.skadnetwork',
            '4dzt52r2t5.skadnetwork',
            'tl55sbb4fm.skadnetwork',
            'c3frkrj4fj.skadnetwork',
            'e5fvkxwrpn.skadnetwork',
            '8c4e2ghe7u.skadnetwork',
            '3rd42ekr43.skadnetwork',
            '97r2b46745.skadnetwork',
            '3qcr597p9d.skadnetwork',
          ],
        },
      ],
      [
        'expo-build-properties',
        {
          ios: {
            useFrameworks: 'static',
          },
        },
      ],
    ],
    splash: {
      image: './assets/splash-icon.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff',
    },
    ios: {
      supportsTablet: true,
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/adaptive-icon.png',
        backgroundColor: '#ffffff',
      },
      package: 'com.rwsoftware.jumpersforgoalposts',
      googleServicesFile: process.env.GOOGLE_SERVICES_JSON ?? './google-services.json',
      edgeToEdgeEnabled: true,
    },
    web: {
      bundler: 'metro',
      favicon: './assets/favicon.png',
      bundleConfig: {
        devtool: 'source-map',
      },
    },
    extra: {
      eas: {
        projectId: 'bfb6811e-e633-425f-90f9-8945b270f4a3',
      },
      appTitle: {
        development: 'Jumpers (Dev)',
        preview: 'Jumpers (Preview)',
        production: 'Jumpers For Goalposts',
      },
    },
    runtimeVersion: {
      policy: 'appVersion',
    },
  },
});
