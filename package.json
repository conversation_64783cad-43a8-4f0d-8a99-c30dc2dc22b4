{"name": "jumpersforgoalpostsnative", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "prebuild": "expo prebuild", "android": "expo run:android", "dev-client": "eas build --platform android --profile development", "ios": "expo run:ios", "web": "expo start --web", "web-debug": "cross-env GENERATE_SOURCEMAP=true expo start --web -c", "lint": "eslint .", "format:check": "prettier --check ./src", "format:fix": "prettier --write ./src", "test": "jest"}, "dependencies": {"@aws-amplify/react-native": "^1.1.7", "@aws-amplify/rtn-web-browser": "^1.1.1", "@aws-amplify/ui-react-native": "^2.5.3", "@expo-google-fonts/nunito": "^0.2.3", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native-stack": "^7.3.10", "@sentry/react-native": "^6.14.0", "@tanstack/react-query": "^5.0.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "aws-amplify": "^6.13.6", "axios": "^1.6.7", "dayjs": "^1.11.13", "expo": "^53.0.9", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-dev-client": "~5.1.8", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-notifications": "~0.31.2", "expo-router": "~5.0.7", "expo-status-bar": "~2.2.3", "lodash.debounce": "^4.0.8", "moti": "^0.30.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-config": "^1.5.5", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-google-mobile-ads": "^15.3.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0", "styled-components": "^6.1.18", "expo-system-ui": "~5.0.7"}, "devDependencies": {"@react-native-community/cli": "latest", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "@typescript-eslint/parser": "^8.26.1", "cross-env": "^7.0.3", "eslint": "^9.19.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "get-tsconfig": "^4.10.0", "jest": "^29.7.0", "jest-environment-node": "^30.0.0-beta.3", "prettier": ">= 3", "prettier-plugin-organize-imports": ">= 3", "react-test-renderer": "^19.0.0", "typescript": "~5.8.3"}, "private": true}