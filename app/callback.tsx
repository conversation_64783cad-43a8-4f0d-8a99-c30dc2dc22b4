import { Hub } from '@aws-amplify/core';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter } from 'expo-router';
import { useEffect } from 'react';
import { View } from 'react-native';
import { NOTIFICATION_SETTINGS_SEEN_KEY } from '../src/context/ManagerContext';

export default function Callback() {
  const router = useRouter();

  useEffect(() => {
    // Listen for auth events
    const listener = Hub.listen('auth', ({ payload }) => {
      const { event } = payload;
      console.log('Auth event in callback:', event);
      if (event === 'signedIn') {
        // Check if user has seen notification settings screen
        const checkNotificationSettingsSeen = async () => {
          try {
            const settingsSeen = await AsyncStorage.getItem(NOTIFICATION_SETTINGS_SEEN_KEY);
            if (settingsSeen === 'true') {
              // User has seen notification settings, go to home screen
              router.replace('/');
            } else {
              // User hasn't seen notification settings, show the screen
              router.replace('/notification-settings');
            }
          } catch (error) {
            console.error('Error checking notification settings status:', error);
            // Default to home screen on error
            router.replace('/');
          }
        };

        checkNotificationSettingsSeen();
      }
    });

    return () => {
      listener();
    };
  }, [router]);

  return <View />;
}
